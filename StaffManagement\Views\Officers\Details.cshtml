@model StaffManagement.Models.Officer

@{
    ViewData["Title"] = "تفاصيل الضابط";
}

<div class="container" dir="rtl">
    <div class="row justify-content-center">
        <div class="col-md-10">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h4 class="card-title mb-0">
                        <i class="fas fa-id-card me-2"></i>
                        بطاقة الضابط
                    </h4>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- الصورة الشخصية -->
                        <div class="col-md-3 text-center">
                            <div class="mb-3">
                                @if (!string.IsNullOrEmpty(Model.PhotoPath))
                                {
                                    <img src="@Model.PhotoPath" alt="صورة @Model.FullName" 
                                         class="img-thumbnail rounded-circle" 
                                         style="width: 200px; height: 200px; object-fit: cover;" />
                                }
                                else
                                {
                                    <div class="bg-secondary rounded-circle d-flex align-items-center justify-content-center mx-auto" 
                                         style="width: 200px; height: 200px;">
                                        <i class="fas fa-user fa-5x text-white"></i>
                                    </div>
                                }
                            </div>
                            <h5 class="text-primary">@Html.DisplayFor(model => model.FullName)</h5>
                            <p class="text-muted">
                                <span class="badge bg-info fs-6">@Html.DisplayFor(model => model.Rank)</span>
                            </p>
                        </div>

                        <!-- البيانات الأساسية -->
                        <div class="col-md-9">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="card border-0 bg-light mb-3">
                                        <div class="card-body">
                                            <h6 class="card-title text-primary">
                                                <i class="fas fa-id-badge me-2"></i>
                                                معلومات الهوية
                                            </h6>
                                            <table class="table table-borderless mb-0">
                                                <tr>
                                                    <td class="fw-bold">رقم الموظف:</td>
                                                    <td>@Html.DisplayFor(model => model.EmployeeNumber)</td>
                                                </tr>
                                                <tr>
                                                    <td class="fw-bold">الرقم الإحصائي:</td>
                                                    <td>@Html.DisplayFor(model => model.StatisticalNumber)</td>
                                                </tr>
                                                <tr>
                                                    <td class="fw-bold">الرتبة:</td>
                                                    <td>
                                                        <span class="badge bg-info">@Html.DisplayFor(model => model.Rank)</span>
                                                    </td>
                                                </tr>
                                            </table>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="card border-0 bg-light mb-3">
                                        <div class="card-body">
                                            <h6 class="card-title text-success">
                                                <i class="fas fa-user me-2"></i>
                                                المعلومات الشخصية
                                            </h6>
                                            <table class="table table-borderless mb-0">
                                                <tr>
                                                    <td class="fw-bold">الاسم الكامل:</td>
                                                    <td>@Html.DisplayFor(model => model.FullName)</td>
                                                </tr>
                                                <tr>
                                                    <td class="fw-bold">محل الولادة:</td>
                                                    <td>@Html.DisplayFor(model => model.PlaceOfBirth)</td>
                                                </tr>
                                                <tr>
                                                    <td class="fw-bold">تاريخ الولادة:</td>
                                                    <td>@Html.DisplayFor(model => model.DateOfBirth)</td>
                                                </tr>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- معلومات العمر -->
                            <div class="row">
                                <div class="col-12">
                                    <div class="card border-0 bg-warning bg-opacity-10 mb-3">
                                        <div class="card-body">
                                            <h6 class="card-title text-warning">
                                                <i class="fas fa-calendar-alt me-2"></i>
                                                معلومات العمر
                                            </h6>
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <p class="mb-1">
                                                        <strong>العمر التفصيلي:</strong>
                                                        <span class="text-primary fs-5">@Html.DisplayFor(model => model.Age)</span>
                                                    </p>
                                                </div>
                                                <div class="col-md-6">
                                                    <p class="mb-1">
                                                        <strong>العمر بالسنوات:</strong>
                                                        <span class="badge bg-warning text-dark fs-6">@Html.DisplayFor(model => model.AgeInYears) سنة</span>
                                                    </p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- معلومات النظام -->
                            <div class="row">
                                <div class="col-12">
                                    <div class="card border-0 bg-secondary bg-opacity-10">
                                        <div class="card-body">
                                            <h6 class="card-title text-secondary">
                                                <i class="fas fa-cog me-2"></i>
                                                معلومات النظام
                                            </h6>
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <small class="text-muted">
                                                        <strong>تاريخ الإضافة:</strong>
                                                        @Model.CreatedAt.ToString("dd/MM/yyyy hh:mm tt")
                                                    </small>
                                                </div>
                                                <div class="col-md-6">
                                                    <small class="text-muted">
                                                        <strong>آخر تحديث:</strong>
                                                        @Model.UpdatedAt.ToString("dd/MM/yyyy hh:mm tt")
                                                    </small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- أزرار العمليات -->
                    <hr />
                    <div class="d-flex justify-content-between">
                        <div>
                            <a asp-action="Edit" asp-route-id="@Model.Id" class="btn btn-warning">
                                <i class="fas fa-edit me-2"></i>
                                تعديل البيانات
                            </a>
                            <a asp-action="Delete" asp-route-id="@Model.Id" class="btn btn-danger ms-2">
                                <i class="fas fa-trash me-2"></i>
                                حذف الضابط
                            </a>
                        </div>
                        <div>
                            <a asp-action="Index" class="btn btn-primary">
                                <i class="fas fa-arrow-left me-2"></i>
                                العودة للقائمة
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        // طباعة البطاقة
        function printCard() {
            window.print();
        }
        
        // إضافة زر الطباعة
        document.addEventListener('DOMContentLoaded', function() {
            const printBtn = document.createElement('button');
            printBtn.className = 'btn btn-info ms-2';
            printBtn.innerHTML = '<i class="fas fa-print me-2"></i>طباعة البطاقة';
            printBtn.onclick = printCard;
            
            const editBtn = document.querySelector('a[href*="Edit"]');
            if (editBtn) {
                editBtn.parentNode.insertBefore(printBtn, editBtn);
            }
        });
    </script>
}
