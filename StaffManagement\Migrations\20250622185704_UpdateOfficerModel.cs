﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace StaffManagement.Migrations
{
    /// <inheritdoc />
    public partial class UpdateOfficerModel : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "FullName",
                table: "Officers");

            migrationBuilder.AddColumn<string>(
                name: "FirstName",
                table: "Officers",
                type: "nvarchar(50)",
                maxLength: 50,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "FourthName",
                table: "Officers",
                type: "nvarchar(50)",
                maxLength: 50,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "LastName",
                table: "Officers",
                type: "nvarchar(50)",
                maxLength: 50,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "SecondName",
                table: "Officers",
                type: "nvarchar(50)",
                maxLength: 50,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "ThirdName",
                table: "Officers",
                type: "nvarchar(50)",
                maxLength: 50,
                nullable: false,
                defaultValue: "");

            migrationBuilder.CreateIndex(
                name: "IX_Officer_FullName",
                table: "Officers",
                columns: new[] { "FirstName", "SecondName", "ThirdName", "FourthName", "LastName" },
                unique: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_Officer_FullName",
                table: "Officers");

            migrationBuilder.DropColumn(
                name: "FirstName",
                table: "Officers");

            migrationBuilder.DropColumn(
                name: "FourthName",
                table: "Officers");

            migrationBuilder.DropColumn(
                name: "LastName",
                table: "Officers");

            migrationBuilder.DropColumn(
                name: "SecondName",
                table: "Officers");

            migrationBuilder.DropColumn(
                name: "ThirdName",
                table: "Officers");

            migrationBuilder.AddColumn<string>(
                name: "FullName",
                table: "Officers",
                type: "nvarchar(200)",
                maxLength: 200,
                nullable: false,
                defaultValue: "");
        }
    }
}
