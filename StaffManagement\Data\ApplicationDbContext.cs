using Microsoft.EntityFrameworkCore;
using StaffManagement.Models;

namespace StaffManagement.Data
{
    public class ApplicationDbContext : DbContext
    {
        public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options) : base(options)
        {
        }

        public DbSet<Officer> Officers { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // تكوين نموذج الضابط
            modelBuilder.Entity<Officer>(entity =>
            {
                entity.HasKey(e => e.Id);
                
                entity.Property(e => e.EmployeeNumber)
                    .IsRequired()
                    .HasMaxLength(50);

                entity.Property(e => e.StatisticalNumber)
                    .IsRequired()
                    .HasMaxLength(50);

                entity.Property(e => e.Rank)
                    .IsRequired()
                    .HasMaxLength(50);

                entity.Property(e => e.FullName)
                    .IsRequired()
                    .HasMaxLength(200);

                entity.Property(e => e.PlaceOfBirth)
                    .IsRequired()
                    .HasMaxLength(100);

                entity.Property(e => e.PhotoPath)
                    .HasMaxLength(500);

                // إنشاء فهرس فريد لرقم الموظف
                entity.HasIndex(e => e.EmployeeNumber)
                    .IsUnique()
                    .HasDatabaseName("IX_Officer_EmployeeNumber");

                // إنشاء فهرس فريد للرقم الإحصائي
                entity.HasIndex(e => e.StatisticalNumber)
                    .IsUnique()
                    .HasDatabaseName("IX_Officer_StatisticalNumber");
            });
        }

        public override int SaveChanges()
        {
            UpdateTimestamps();
            return base.SaveChanges();
        }

        public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
        {
            UpdateTimestamps();
            return await base.SaveChangesAsync(cancellationToken);
        }

        private void UpdateTimestamps()
        {
            var entries = ChangeTracker.Entries<Officer>();

            foreach (var entry in entries)
            {
                if (entry.State == EntityState.Added)
                {
                    entry.Entity.CreatedAt = DateTime.Now;
                    entry.Entity.UpdatedAt = DateTime.Now;
                }
                else if (entry.State == EntityState.Modified)
                {
                    entry.Entity.UpdatedAt = DateTime.Now;
                }
            }
        }
    }
}
