# نظام إدارة الضباط والمنتسبين

## وصف المشروع

نظام شامل لإدارة بيانات الضباط والمنتسبين مطور باستخدام ASP.NET Core مع Entity Framework Core. يوفر النظام واجهة سهلة الاستخدام باللغة العربية لإدارة جميع البيانات المطلوبة.

## الميزات الرئيسية

### ✅ إدارة البيانات الأساسية
- **رقم الموظف**: رقم فريد لكل موظف
- **الرقم الإحصائي**: رقم إحصائي فريد
- **الرتبة**: مع قائمة منسدلة للرتب العسكرية
- **الاسم الرباعي واللقب**: الاسم الكامل
- **محل الولادة**: مكان الولادة
- **تاريخ الولادة**: مع حساب العمر تلقائياً
- **صورة الموظف**: رفع وعرض الصور الشخصية

### ✅ حساب العمر التلقائي
- العمر بالسنوات والشهور والأيام
- العمر بالسنوات فقط
- تحديث تلقائي عند تغيير تاريخ الولادة

### ✅ إدارة الصور
- رفع صور الموظفين
- معاينة الصور قبل الحفظ
- حذف الصور القديمة عند التحديث
- عرض صورة افتراضية عند عدم وجود صورة

### ✅ البحث والفلترة
- البحث بالاسم
- البحث برقم الموظف
- البحث بالرقم الإحصائي
- البحث بالرتبة
- ترتيب النتائج حسب مختلف الحقول

### ✅ واجهة المستخدم
- تصميم متجاوب (Responsive)
- دعم كامل للغة العربية (RTL)
- استخدام Bootstrap 5
- أيقونات Font Awesome
- رسائل تأكيد وتحذير
- تجربة مستخدم محسنة

## التقنيات المستخدمة

- **ASP.NET Core 9.0**: إطار العمل الرئيسي
- **Entity Framework Core**: للتعامل مع قاعدة البيانات
- **SQL Server LocalDB**: قاعدة البيانات
- **Bootstrap 5 RTL**: للتصميم المتجاوب
- **Font Awesome**: للأيقونات
- **jQuery**: للتفاعل مع الواجهة
- **Google Fonts (Cairo)**: للخطوط العربية

## متطلبات التشغيل

- .NET 9.0 SDK أو أحدث
- SQL Server LocalDB
- متصفح ويب حديث

## طريقة التشغيل

### 1. استنساخ المشروع
```bash
git clone [repository-url]
cd StaffManagement
```

### 2. استعادة الحزم
```bash
dotnet restore
```

### 3. إنشاء قاعدة البيانات
```bash
dotnet ef database update
```

### 4. تشغيل التطبيق
```bash
dotnet run
```

### 5. فتح المتصفح
انتقل إلى: `http://localhost:5000`

## هيكل المشروع

```
StaffManagement/
├── Controllers/
│   └── OfficersController.cs      # تحكم في عمليات الضباط
├── Data/
│   └── ApplicationDbContext.cs    # سياق قاعدة البيانات
├── Models/
│   └── Officer.cs                 # نموذج بيانات الضابط
├── Views/
│   ├── Officers/                  # صفحات إدارة الضباط
│   │   ├── Index.cshtml          # قائمة الضباط
│   │   ├── Create.cshtml         # إضافة ضابط جديد
│   │   ├── Details.cshtml        # تفاصيل الضابط
│   │   ├── Edit.cshtml           # تعديل بيانات الضابط
│   │   └── Delete.cshtml         # حذف الضابط
│   └── Shared/
│       ├── _Layout.cshtml        # التخطيط الرئيسي
│       └── _ValidationScriptsPartial.cshtml
├── wwwroot/
│   └── images/
│       └── officers/             # مجلد صور الضباط
├── Migrations/                   # ملفات الهجرة
├── appsettings.json             # إعدادات التطبيق
└── Program.cs                   # نقطة البداية
```

## قاعدة البيانات

### جدول Officers
| العمود | النوع | الوصف |
|--------|-------|-------|
| Id | int | المفتاح الأساسي |
| EmployeeNumber | nvarchar(50) | رقم الموظف (فريد) |
| StatisticalNumber | nvarchar(50) | الرقم الإحصائي (فريد) |
| Rank | nvarchar(50) | الرتبة |
| FullName | nvarchar(200) | الاسم الكامل |
| PlaceOfBirth | nvarchar(100) | محل الولادة |
| DateOfBirth | datetime2 | تاريخ الولادة |
| PhotoPath | nvarchar(500) | مسار الصورة |
| CreatedAt | datetime2 | تاريخ الإنشاء |
| UpdatedAt | datetime2 | تاريخ آخر تحديث |

## الميزات المستقبلية

- [ ] تصدير البيانات إلى Excel/PDF
- [ ] نظام النسخ الاحتياطي
- [ ] تقارير مفصلة وإحصائيات
- [ ] نظام المستخدمين والصلاحيات
- [ ] تتبع تاريخ التعديلات
- [ ] إشعارات أعياد الميلاد
- [ ] البحث المتقدم بفلاتر متعددة

## المساهمة

نرحب بالمساهمات! يرجى:
1. عمل Fork للمشروع
2. إنشاء فرع جديد للميزة
3. إجراء التعديلات
4. إرسال Pull Request

## الترخيص

هذا المشروع مرخص تحت رخصة MIT.

## الدعم

للحصول على الدعم أو الإبلاغ عن مشاكل، يرجى إنشاء Issue في المستودع.

---

**تم تطوير هذا النظام باستخدام أحدث التقنيات لضمان الأداء والأمان والسهولة في الاستخدام.**
