using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using StaffManagement.Data;
using StaffManagement.Models;
using StaffManagement.Services;

namespace StaffManagement.Controllers
{
    public class OfficersController : Controller
    {
        private readonly ApplicationDbContext _context;
        private readonly IWebHostEnvironment _webHostEnvironment;
        private readonly IEmployeeNumberService _employeeNumberService;

        public OfficersController(ApplicationDbContext context, IWebHostEnvironment webHostEnvironment, IEmployeeNumberService employeeNumberService)
        {
            _context = context;
            _webHostEnvironment = webHostEnvironment;
            _employeeNumberService = employeeNumberService;
        }

        // GET: Officers
        public async Task<IActionResult> Index(string searchString, string sortOrder)
        {
            ViewData["NameSortParm"] = String.IsNullOrEmpty(sortOrder) ? "name_desc" : "";
            ViewData["RankSortParm"] = sortOrder == "Rank" ? "rank_desc" : "Rank";
            ViewData["AgeSortParm"] = sortOrder == "Age" ? "age_desc" : "Age";
            ViewData["CurrentFilter"] = searchString;

            var officers = from o in _context.Officers
                          select o;

            if (!String.IsNullOrEmpty(searchString))
            {
                officers = officers.Where(o => o.FullName.Contains(searchString)
                                       || o.EmployeeNumber.Contains(searchString)
                                       || o.StatisticalNumber.Contains(searchString)
                                       || o.Rank.Contains(searchString));
            }

            switch (sortOrder)
            {
                case "name_desc":
                    officers = officers.OrderByDescending(o => o.FullName);
                    break;
                case "Rank":
                    officers = officers.OrderBy(o => o.Rank);
                    break;
                case "rank_desc":
                    officers = officers.OrderByDescending(o => o.Rank);
                    break;
                case "Age":
                    officers = officers.OrderBy(o => o.DateOfBirth);
                    break;
                case "age_desc":
                    officers = officers.OrderByDescending(o => o.DateOfBirth);
                    break;
                default:
                    officers = officers.OrderBy(o => o.FullName);
                    break;
            }

            return View(await officers.AsNoTracking().ToListAsync());
        }

        // GET: Officers/Details/5
        public async Task<IActionResult> Details(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var officer = await _context.Officers
                .FirstOrDefaultAsync(m => m.Id == id);
            if (officer == null)
            {
                return NotFound();
            }

            return View(officer);
        }

        // GET: Officers/Create
        public IActionResult Create()
        {
            return View();
        }

        // POST: Officers/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create([Bind("StatisticalNumber,Rank,FirstName,SecondName,ThirdName,FourthName,LastName,PlaceOfBirth,DateOfBirth")] Officer officer, IFormFile? photo)
        {
            if (ModelState.IsValid)
            {
                // توليد رقم الموظف تلقائياً
                officer.EmployeeNumber = await _employeeNumberService.GenerateEmployeeNumberAsync();

                // التحقق من عدم تكرار الرقم الإحصائي
                if (await _context.Officers.AnyAsync(o => o.StatisticalNumber == officer.StatisticalNumber))
                {
                    ModelState.AddModelError("StatisticalNumber", "الرقم الإحصائي موجود مسبقاً");
                    return View(officer);
                }

                // التحقق من عدم تكرار الاسم الكامل
                if (await _context.Officers.AnyAsync(o => o.FirstName == officer.FirstName &&
                                                         o.SecondName == officer.SecondName &&
                                                         o.ThirdName == officer.ThirdName &&
                                                         o.FourthName == officer.FourthName &&
                                                         o.LastName == officer.LastName))
                {
                    ModelState.AddModelError("", "الاسم الكامل موجود مسبقاً");
                    return View(officer);
                }

                // رفع الصورة إذا تم اختيارها
                if (photo != null && photo.Length > 0)
                {
                    var uploadsFolder = Path.Combine(_webHostEnvironment.WebRootPath, "images", "officers");
                    Directory.CreateDirectory(uploadsFolder);

                    var uniqueFileName = Guid.NewGuid().ToString() + "_" + photo.FileName;
                    var filePath = Path.Combine(uploadsFolder, uniqueFileName);

                    using (var fileStream = new FileStream(filePath, FileMode.Create))
                    {
                        await photo.CopyToAsync(fileStream);
                    }

                    officer.PhotoPath = "/images/officers/" + uniqueFileName;
                }

                _context.Add(officer);
                await _context.SaveChangesAsync();
                TempData["SuccessMessage"] = "تم إضافة الضابط بنجاح";
                return RedirectToAction(nameof(Index));
            }
            return View(officer);
        }

        // GET: Officers/Edit/5
        public async Task<IActionResult> Edit(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var officer = await _context.Officers.FindAsync(id);
            if (officer == null)
            {
                return NotFound();
            }
            return View(officer);
        }

        // POST: Officers/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, [Bind("Id,EmployeeNumber,StatisticalNumber,Rank,FirstName,SecondName,ThirdName,FourthName,LastName,PlaceOfBirth,DateOfBirth,PhotoPath")] Officer officer, IFormFile? photo)
        {
            if (id != officer.Id)
            {
                return NotFound();
            }

            if (ModelState.IsValid)
            {
                try
                {
                    // التحقق من عدم تكرار الرقم الإحصائي (باستثناء الضابط الحالي)
                    if (await _context.Officers.AnyAsync(o => o.StatisticalNumber == officer.StatisticalNumber && o.Id != officer.Id))
                    {
                        ModelState.AddModelError("StatisticalNumber", "الرقم الإحصائي موجود مسبقاً");
                        return View(officer);
                    }

                    // التحقق من عدم تكرار الاسم الكامل (باستثناء الضابط الحالي)
                    if (await _context.Officers.AnyAsync(o => o.FirstName == officer.FirstName &&
                                                             o.SecondName == officer.SecondName &&
                                                             o.ThirdName == officer.ThirdName &&
                                                             o.FourthName == officer.FourthName &&
                                                             o.LastName == officer.LastName &&
                                                             o.Id != officer.Id))
                    {
                        ModelState.AddModelError("", "الاسم الكامل موجود مسبقاً");
                        return View(officer);
                    }

                    // رفع صورة جديدة إذا تم اختيارها
                    if (photo != null && photo.Length > 0)
                    {
                        // حذف الصورة القديمة إذا كانت موجودة
                        if (!string.IsNullOrEmpty(officer.PhotoPath))
                        {
                            var oldPhotoPath = Path.Combine(_webHostEnvironment.WebRootPath, officer.PhotoPath.TrimStart('/'));
                            if (System.IO.File.Exists(oldPhotoPath))
                            {
                                System.IO.File.Delete(oldPhotoPath);
                            }
                        }

                        var uploadsFolder = Path.Combine(_webHostEnvironment.WebRootPath, "images", "officers");
                        Directory.CreateDirectory(uploadsFolder);

                        var uniqueFileName = Guid.NewGuid().ToString() + "_" + photo.FileName;
                        var filePath = Path.Combine(uploadsFolder, uniqueFileName);

                        using (var fileStream = new FileStream(filePath, FileMode.Create))
                        {
                            await photo.CopyToAsync(fileStream);
                        }

                        officer.PhotoPath = "/images/officers/" + uniqueFileName;
                    }

                    _context.Update(officer);
                    await _context.SaveChangesAsync();
                    TempData["SuccessMessage"] = "تم تحديث بيانات الضابط بنجاح";
                }
                catch (DbUpdateConcurrencyException)
                {
                    if (!OfficerExists(officer.Id))
                    {
                        return NotFound();
                    }
                    else
                    {
                        throw;
                    }
                }
                return RedirectToAction(nameof(Index));
            }
            return View(officer);
        }

        // GET: Officers/Delete/5
        public async Task<IActionResult> Delete(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var officer = await _context.Officers
                .FirstOrDefaultAsync(m => m.Id == id);
            if (officer == null)
            {
                return NotFound();
            }

            return View(officer);
        }

        // POST: Officers/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            var officer = await _context.Officers.FindAsync(id);
            if (officer != null)
            {
                // حذف صورة الضابط إذا كانت موجودة
                if (!string.IsNullOrEmpty(officer.PhotoPath))
                {
                    var photoPath = Path.Combine(_webHostEnvironment.WebRootPath, officer.PhotoPath.TrimStart('/'));
                    if (System.IO.File.Exists(photoPath))
                    {
                        System.IO.File.Delete(photoPath);
                    }
                }

                _context.Officers.Remove(officer);
                await _context.SaveChangesAsync();
                TempData["SuccessMessage"] = "تم حذف الضابط بنجاح";
            }

            return RedirectToAction(nameof(Index));
        }

        private bool OfficerExists(int id)
        {
            return _context.Officers.Any(e => e.Id == id);
        }
    }
}
