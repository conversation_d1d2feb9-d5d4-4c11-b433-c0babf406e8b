using Microsoft.EntityFrameworkCore;
using StaffManagement.Data;

namespace StaffManagement.Services
{
    public interface IEmployeeNumberService
    {
        Task<string> GenerateEmployeeNumberAsync();
    }

    public class EmployeeNumberService : IEmployeeNumberService
    {
        private readonly ApplicationDbContext _context;

        public EmployeeNumberService(ApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<string> GenerateEmployeeNumberAsync()
        {
            // البحث عن آخر تسلسل
            var lastEmployeeNumber = await _context.Officers
                .OrderByDescending(o => o.Id)
                .Select(o => o.EmployeeNumber)
                .FirstOrDefaultAsync();

            int nextSequence = 1;

            if (!string.IsNullOrEmpty(lastEmployeeNumber))
            {
                // استخراج الرقم التسلسلي
                if (int.TryParse(lastEmployeeNumber, out int lastSequence))
                {
                    nextSequence = lastSequence + 1;
                }
            }

            return nextSequence.ToString();
        }
    }
}
