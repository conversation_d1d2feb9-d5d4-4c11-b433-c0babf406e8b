using Microsoft.EntityFrameworkCore;
using StaffManagement.Data;

namespace StaffManagement.Services
{
    public interface IEmployeeNumberService
    {
        Task<string> GenerateEmployeeNumberAsync();
    }

    public class EmployeeNumberService : IEmployeeNumberService
    {
        private readonly ApplicationDbContext _context;

        public EmployeeNumberService(ApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<string> GenerateEmployeeNumberAsync()
        {
            var currentYear = DateTime.Now.Year;
            var prefix = $"MOI-{currentYear}-";

            // البحث عن آخر رقم موظف في السنة الحالية
            var lastEmployeeNumber = await _context.Officers
                .Where(o => o.EmployeeNumber.StartsWith(prefix))
                .OrderByDescending(o => o.EmployeeNumber)
                .Select(o => o.EmployeeNumber)
                .FirstOrDefaultAsync();

            int nextSequence = 1;

            if (!string.IsNullOrEmpty(lastEmployeeNumber))
            {
                // استخراج الرقم التسلسلي من آخر رقم موظف
                var sequencePart = lastEmployeeNumber.Substring(prefix.Length);
                if (int.TryParse(sequencePart, out int lastSequence))
                {
                    nextSequence = lastSequence + 1;
                }
            }

            // تنسيق الرقم التسلسلي بـ 6 أرقام
            var formattedSequence = nextSequence.ToString("D6");
            
            return $"{prefix}{formattedSequence}";
        }
    }
}
