using Microsoft.EntityFrameworkCore;
using StaffManagement.Models;

namespace StaffManagement.Data
{
    public static class SeedData
    {
        public static void Initialize(IServiceProvider serviceProvider)
        {
            using var context = new ApplicationDbContext(
                serviceProvider.GetRequiredService<DbContextOptions<ApplicationDbContext>>());

            // التحقق من وجود بيانات مسبقة
            if (context.Officers.Any())
            {
                return; // قاعدة البيانات تحتوي على بيانات بالفعل
            }

            // إضافة بيانات تجريبية
            var officers = new Officer[]
            {
                new Officer
                {
                    EmployeeNumber = "MOI-2025-000001",
                    StatisticalNumber = "1001",
                    Rank = "عقيد",
                    FirstName = "أحمد",
                    SecondName = "محمد",
                    ThirdName = "علي",
                    FourthName = "حسن",
                    LastName = "الأحمد",
                    PlaceOfBirth = "بغداد",
                    DateOfBirth = new DateTime(1980, 5, 15),
                    CreatedAt = DateTime.Now,
                    UpdatedAt = DateTime.Now
                },
                new Officer
                {
                    EmployeeNumber = "MOI-2025-000002",
                    StatisticalNumber = "1002",
                    Rank = "مقدم",
                    FirstName = "محمد",
                    SecondName = "عبدالله",
                    ThirdName = "حسن",
                    FourthName = "علي",
                    LastName = "المحمد",
                    PlaceOfBirth = "البصرة",
                    DateOfBirth = new DateTime(1985, 8, 22),
                    CreatedAt = DateTime.Now,
                    UpdatedAt = DateTime.Now
                },
                new Officer
                {
                    EmployeeNumber = "MOI-2025-000003",
                    StatisticalNumber = "1003",
                    Rank = "رائد",
                    FirstName = "علي",
                    SecondName = "حسين",
                    ThirdName = "جعفر",
                    FourthName = "محمد",
                    LastName = "العلي",
                    PlaceOfBirth = "الموصل",
                    DateOfBirth = new DateTime(1988, 12, 10),
                    CreatedAt = DateTime.Now,
                    UpdatedAt = DateTime.Now
                },
                new Officer
                {
                    EmployeeNumber = "MOI-2025-000004",
                    StatisticalNumber = "1004",
                    Rank = "نقيب",
                    FirstName = "حسن",
                    SecondName = "عبدالرحمن",
                    ThirdName = "طه",
                    FourthName = "عبدالله",
                    LastName = "الحسن",
                    PlaceOfBirth = "أربيل",
                    DateOfBirth = new DateTime(1990, 3, 8),
                    CreatedAt = DateTime.Now,
                    UpdatedAt = DateTime.Now
                },
                new Officer
                {
                    EmployeeNumber = "MOI-2025-000005",
                    StatisticalNumber = "1005",
                    Rank = "ملازم أول",
                    FirstName = "عمر",
                    SecondName = "فاروق",
                    ThirdName = "سالم",
                    FourthName = "أحمد",
                    LastName = "العمر",
                    PlaceOfBirth = "كربلاء",
                    DateOfBirth = new DateTime(1992, 7, 18),
                    CreatedAt = DateTime.Now,
                    UpdatedAt = DateTime.Now
                }
            };

            context.Officers.AddRange(officers);
            context.SaveChanges();
        }
    }
}
