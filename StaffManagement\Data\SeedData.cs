using Microsoft.EntityFrameworkCore;
using StaffManagement.Models;

namespace StaffManagement.Data
{
    public static class SeedData
    {
        public static void Initialize(IServiceProvider serviceProvider)
        {
            using var context = new ApplicationDbContext(
                serviceProvider.GetRequiredService<DbContextOptions<ApplicationDbContext>>());

            // التحقق من وجود بيانات مسبقة
            if (context.Officers.Any())
            {
                return; // قاعدة البيانات تحتوي على بيانات بالفعل
            }

            // إضافة بيانات تجريبية
            var officers = new Officer[]
            {
                new Officer
                {
                    EmployeeNumber = "001",
                    StatisticalNumber = "ST001",
                    Rank = "عقيد",
                    FullName = "أحمد محمد علي الأحمد",
                    PlaceOfBirth = "بغداد",
                    DateOfBirth = new DateTime(1980, 5, 15),
                    CreatedAt = DateTime.Now,
                    UpdatedAt = DateTime.Now
                },
                new Officer
                {
                    EmployeeNumber = "002",
                    StatisticalNumber = "ST002",
                    Rank = "مقدم",
                    FullName = "محمد عبدالله حسن المحمد",
                    PlaceOfBirth = "البصرة",
                    DateOfBirth = new DateTime(1985, 8, 22),
                    CreatedAt = DateTime.Now,
                    UpdatedAt = DateTime.Now
                },
                new Officer
                {
                    EmployeeNumber = "003",
                    StatisticalNumber = "ST003",
                    Rank = "رائد",
                    FullName = "علي حسين جعفر العلي",
                    PlaceOfBirth = "الموصل",
                    DateOfBirth = new DateTime(1988, 12, 10),
                    CreatedAt = DateTime.Now,
                    UpdatedAt = DateTime.Now
                },
                new Officer
                {
                    EmployeeNumber = "004",
                    StatisticalNumber = "ST004",
                    Rank = "نقيب",
                    FullName = "حسن عبدالرحمن طه الحسن",
                    PlaceOfBirth = "أربيل",
                    DateOfBirth = new DateTime(1990, 3, 8),
                    CreatedAt = DateTime.Now,
                    UpdatedAt = DateTime.Now
                },
                new Officer
                {
                    EmployeeNumber = "005",
                    StatisticalNumber = "ST005",
                    Rank = "ملازم أول",
                    FullName = "عمر فاروق سالم العمر",
                    PlaceOfBirth = "كربلاء",
                    DateOfBirth = new DateTime(1992, 7, 18),
                    CreatedAt = DateTime.Now,
                    UpdatedAt = DateTime.Now
                },
                new Officer
                {
                    EmployeeNumber = "006",
                    StatisticalNumber = "ST006",
                    Rank = "ملازم",
                    FullName = "يوسف إبراهيم محمود اليوسف",
                    PlaceOfBirth = "النجف",
                    DateOfBirth = new DateTime(1994, 11, 25),
                    CreatedAt = DateTime.Now,
                    UpdatedAt = DateTime.Now
                },
                new Officer
                {
                    EmployeeNumber = "007",
                    StatisticalNumber = "ST007",
                    Rank = "رقيب أول",
                    FullName = "خالد سعد عبدالله الخالد",
                    PlaceOfBirth = "ديالى",
                    DateOfBirth = new DateTime(1987, 4, 12),
                    CreatedAt = DateTime.Now,
                    UpdatedAt = DateTime.Now
                },
                new Officer
                {
                    EmployeeNumber = "008",
                    StatisticalNumber = "ST008",
                    Rank = "رقيب",
                    FullName = "سعد محمد عبدالعزيز السعد",
                    PlaceOfBirth = "الأنبار",
                    DateOfBirth = new DateTime(1989, 9, 30),
                    CreatedAt = DateTime.Now,
                    UpdatedAt = DateTime.Now
                },
                new Officer
                {
                    EmployeeNumber = "009",
                    StatisticalNumber = "ST009",
                    Rank = "عريف",
                    FullName = "طارق عادل حميد الطارق",
                    PlaceOfBirth = "صلاح الدين",
                    DateOfBirth = new DateTime(1991, 6, 14),
                    CreatedAt = DateTime.Now,
                    UpdatedAt = DateTime.Now
                },
                new Officer
                {
                    EmployeeNumber = "010",
                    StatisticalNumber = "ST010",
                    Rank = "جندي أول",
                    FullName = "وليد نبيل فاضل الوليد",
                    PlaceOfBirth = "واسط",
                    DateOfBirth = new DateTime(1995, 1, 20),
                    CreatedAt = DateTime.Now,
                    UpdatedAt = DateTime.Now
                }
            };

            context.Officers.AddRange(officers);
            context.SaveChanges();
        }
    }
}
