/* Please see documentation at https://learn.microsoft.com/aspnet/core/client-side/bundling-and-minification
for details on configuring this project to bundle and minify static web assets. */

a.navbar-brand[b-dse2fpd0rl] {
  white-space: normal;
  text-align: center;
  word-break: break-all;
}

a[b-dse2fpd0rl] {
  color: #0077cc;
}

.btn-primary[b-dse2fpd0rl] {
  color: #fff;
  background-color: #1b6ec2;
  border-color: #1861ac;
}

.nav-pills .nav-link.active[b-dse2fpd0rl], .nav-pills .show > .nav-link[b-dse2fpd0rl] {
  color: #fff;
  background-color: #1b6ec2;
  border-color: #1861ac;
}

.border-top[b-dse2fpd0rl] {
  border-top: 1px solid #e5e5e5;
}
.border-bottom[b-dse2fpd0rl] {
  border-bottom: 1px solid #e5e5e5;
}

.box-shadow[b-dse2fpd0rl] {
  box-shadow: 0 .25rem .75rem rgba(0, 0, 0, .05);
}

button.accept-policy[b-dse2fpd0rl] {
  font-size: 1rem;
  line-height: inherit;
}

.footer[b-dse2fpd0rl] {
  position: absolute;
  bottom: 0;
  width: 100%;
  white-space: nowrap;
  line-height: 60px;
}
