@model StaffManagement.Models.Officer

@{
    ViewData["Title"] = "حذف الضابط";
}

<div class="container" dir="rtl">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card border-danger">
                <div class="card-header bg-danger text-white">
                    <h4 class="card-title mb-0">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        تأكيد حذف الضابط
                    </h4>
                </div>
                <div class="card-body">
                    <div class="alert alert-warning">
                        <i class="fas fa-warning me-2"></i>
                        <strong>تحذير:</strong> هذه العملية لا يمكن التراجع عنها. سيتم حذف جميع بيانات الضابط نهائياً.
                    </div>

                    <div class="row">
                        <!-- صورة الضابط -->
                        <div class="col-md-3 text-center">
                            @if (!string.IsNullOrEmpty(Model.PhotoPath))
                            {
                                <img src="@Model.PhotoPath" alt="صورة @Model.FullName" 
                                     class="img-thumbnail rounded-circle" 
                                     style="width: 150px; height: 150px; object-fit: cover;" />
                            }
                            else
                            {
                                <div class="bg-secondary rounded-circle d-flex align-items-center justify-content-center mx-auto" 
                                     style="width: 150px; height: 150px;">
                                    <i class="fas fa-user fa-3x text-white"></i>
                                </div>
                            }
                        </div>

                        <!-- بيانات الضابط -->
                        <div class="col-md-9">
                            <h5 class="text-danger mb-3">البيانات المراد حذفها:</h5>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <table class="table table-borderless">
                                        <tr>
                                            <td class="fw-bold">الاسم الكامل:</td>
                                            <td>@Html.DisplayFor(model => model.FullName)</td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold">الرتبة:</td>
                                            <td>
                                                <span class="badge bg-info">@Html.DisplayFor(model => model.Rank)</span>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold">رقم الموظف:</td>
                                            <td>@Html.DisplayFor(model => model.EmployeeNumber)</td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold">الرقم الإحصائي:</td>
                                            <td>@Html.DisplayFor(model => model.StatisticalNumber)</td>
                                        </tr>
                                    </table>
                                </div>
                                <div class="col-md-6">
                                    <table class="table table-borderless">
                                        <tr>
                                            <td class="fw-bold">محل الولادة:</td>
                                            <td>@Html.DisplayFor(model => model.PlaceOfBirth)</td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold">تاريخ الولادة:</td>
                                            <td>@Html.DisplayFor(model => model.DateOfBirth)</td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold">العمر:</td>
                                            <td>@Html.DisplayFor(model => model.Age)</td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold">تاريخ الإضافة:</td>
                                            <td>@Model.CreatedAt.ToString("dd/MM/yyyy")</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <hr />

                    <!-- نموذج التأكيد -->
                    <div class="row">
                        <div class="col-12">
                            <div class="bg-light p-3 rounded">
                                <h6 class="text-danger">للمتابعة، يرجى كتابة "تأكيد الحذف" في المربع أدناه:</h6>
                                <input type="text" id="confirmText" class="form-control mb-3" 
                                       placeholder="اكتب: تأكيد الحذف" />
                                <small class="text-muted">هذا للتأكد من أنك تريد حذف هذا الضابط فعلاً</small>
                            </div>
                        </div>
                    </div>

                    <!-- أزرار العمليات -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <form asp-action="Delete" method="post" id="deleteForm">
                                        <input type="hidden" asp-for="Id" />
                                        <button type="submit" class="btn btn-danger" id="deleteBtn" disabled>
                                            <i class="fas fa-trash me-2"></i>
                                            تأكيد الحذف
                                        </button>
                                    </form>
                                </div>
                                <div>
                                    <a asp-action="Details" asp-route-id="@Model.Id" class="btn btn-info me-2">
                                        <i class="fas fa-eye me-2"></i>
                                        عرض التفاصيل
                                    </a>
                                    <a asp-action="Index" class="btn btn-secondary">
                                        <i class="fas fa-arrow-left me-2"></i>
                                        إلغاء والعودة
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        // تفعيل زر الحذف عند كتابة النص الصحيح
        document.getElementById('confirmText').addEventListener('input', function(e) {
            const deleteBtn = document.getElementById('deleteBtn');
            const confirmText = e.target.value.trim();
            
            if (confirmText === 'تأكيد الحذف') {
                deleteBtn.disabled = false;
                deleteBtn.classList.remove('btn-danger');
                deleteBtn.classList.add('btn-outline-danger');
            } else {
                deleteBtn.disabled = true;
                deleteBtn.classList.remove('btn-outline-danger');
                deleteBtn.classList.add('btn-danger');
            }
        });

        // تأكيد إضافي عند الإرسال
        document.getElementById('deleteForm').addEventListener('submit', function(e) {
            const officerName = '@Model.FullName';
            const confirmMessage = `هل أنت متأكد من حذف الضابط "${officerName}" نهائياً؟\n\nهذه العملية لا يمكن التراجع عنها!`;
            
            if (!confirm(confirmMessage)) {
                e.preventDefault();
            }
        });

        // تركيز على مربع النص عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('confirmText').focus();
        });
    </script>
}
