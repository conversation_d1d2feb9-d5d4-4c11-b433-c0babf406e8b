using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace StaffManagement.Models
{
    public class Officer
    {
        [Key]
        public int Id { get; set; }

        [Display(Name = "رقم الموظف")]
        public string EmployeeNumber { get; set; } = string.Empty;

        [Required(ErrorMessage = "الرقم الإحصائي مطلوب")]
        [Display(Name = "الرقم الإحصائي")]
        public string StatisticalNumber { get; set; } = string.Empty;

        [Required(ErrorMessage = "الرتبة مطلوبة")]
        [Display(Name = "الرتبة")]
        public string Rank { get; set; } = string.Empty;

        [Required(ErrorMessage = "الاسم الأول مطلوب")]
        [Display(Name = "الاسم الأول")]
        [StringLength(50)]
        public string FirstName { get; set; } = string.Empty;

        [Required(ErrorMessage = "الاسم الثاني مطلوب")]
        [Display(Name = "الاسم الثاني")]
        [StringLength(50)]
        public string SecondName { get; set; } = string.Empty;

        [Required(ErrorMessage = "الاسم الثالث مطلوب")]
        [Display(Name = "الاسم الثالث")]
        [StringLength(50)]
        public string ThirdName { get; set; } = string.Empty;

        [Required(ErrorMessage = "الاسم الرابع مطلوب")]
        [Display(Name = "الاسم الرابع")]
        [StringLength(50)]
        public string FourthName { get; set; } = string.Empty;

        [Required(ErrorMessage = "اللقب مطلوب")]
        [Display(Name = "اللقب")]
        [StringLength(50)]
        public string LastName { get; set; } = string.Empty;

        [Required(ErrorMessage = "محل الولادة مطلوب")]
        [Display(Name = "محل الولادة")]
        [StringLength(100)]
        public string PlaceOfBirth { get; set; } = string.Empty;

        [Required(ErrorMessage = "تاريخ الولادة مطلوب")]
        [Display(Name = "تاريخ الولادة")]
        [DataType(DataType.Date)]
        public DateTime DateOfBirth { get; set; }

        [Display(Name = "صورة الموظف")]
        public string? PhotoPath { get; set; }

        [NotMapped]
        [Display(Name = "الاسم الكامل")]
        public string FullName
        {
            get
            {
                return $"{FirstName} {SecondName} {ThirdName} {FourthName} {LastName}".Trim();
            }
        }

        [NotMapped]
        [Display(Name = "العمر")]
        public string Age
        {
            get
            {
                var today = DateTime.Today;
                var age = today.Year - DateOfBirth.Year;

                // التحقق من عدم حدوث عيد الميلاد بعد
                if (DateOfBirth.Date > today.AddYears(-age))
                    age--;

                var nextBirthday = DateOfBirth.AddYears(age + 1);
                var daysUntilBirthday = (nextBirthday - today).Days;

                // حساب الأشهر والأيام المتبقية
                var months = 0;
                var days = 0;

                var tempDate = DateOfBirth.AddYears(age);
                while (tempDate.AddMonths(months + 1) <= today)
                {
                    months++;
                }

                tempDate = tempDate.AddMonths(months);
                days = (today - tempDate).Days;

                return $"{age} سنة، {months} شهر، {days} يوم";
            }
        }

        [NotMapped]
        [Display(Name = "العمر بالسنوات")]
        public int AgeInYears
        {
            get
            {
                var today = DateTime.Today;
                var age = today.Year - DateOfBirth.Year;
                if (DateOfBirth.Date > today.AddYears(-age))
                    age--;
                return age;
            }
        }

        public DateTime CreatedAt { get; set; } = DateTime.Now;
        public DateTime UpdatedAt { get; set; } = DateTime.Now;
    }
}
