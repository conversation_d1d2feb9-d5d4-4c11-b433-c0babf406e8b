using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace StaffManagement.Models
{
    public class Officer
    {
        [Key]
        public int Id { get; set; }

        [Required(ErrorMessage = "رقم الموظف مطلوب")]
        [Display(Name = "رقم الموظف")]
        public string EmployeeNumber { get; set; } = string.Empty;

        [Required(ErrorMessage = "الرقم الإحصائي مطلوب")]
        [Display(Name = "الرقم الإحصائي")]
        public string StatisticalNumber { get; set; } = string.Empty;

        [Required(ErrorMessage = "الرتبة مطلوبة")]
        [Display(Name = "الرتبة")]
        public string Rank { get; set; } = string.Empty;

        [Required(ErrorMessage = "الاسم الرباعي واللقب مطلوب")]
        [Display(Name = "الاسم الرباعي واللقب")]
        [StringLength(200)]
        public string FullName { get; set; } = string.Empty;

        [Required(ErrorMessage = "محل الولادة مطلوب")]
        [Display(Name = "محل الولادة")]
        [StringLength(100)]
        public string PlaceOfBirth { get; set; } = string.Empty;

        [Required(ErrorMessage = "تاريخ الولادة مطلوب")]
        [Display(Name = "تاريخ الولادة")]
        [DataType(DataType.Date)]
        public DateTime DateOfBirth { get; set; }

        [Display(Name = "صورة الموظف")]
        public string? PhotoPath { get; set; }

        [NotMapped]
        [Display(Name = "العمر")]
        public string Age
        {
            get
            {
                var today = DateTime.Today;
                var age = today.Year - DateOfBirth.Year;
                
                // التحقق من عدم حدوث عيد الميلاد بعد
                if (DateOfBirth.Date > today.AddYears(-age))
                    age--;

                var nextBirthday = DateOfBirth.AddYears(age + 1);
                var daysUntilBirthday = (nextBirthday - today).Days;
                
                // حساب الأشهر والأيام المتبقية
                var months = 0;
                var days = 0;
                
                var tempDate = DateOfBirth.AddYears(age);
                while (tempDate.AddMonths(months + 1) <= today)
                {
                    months++;
                }
                
                tempDate = tempDate.AddMonths(months);
                days = (today - tempDate).Days;

                return $"{age} سنة، {months} شهر، {days} يوم";
            }
        }

        [NotMapped]
        [Display(Name = "العمر بالسنوات")]
        public int AgeInYears
        {
            get
            {
                var today = DateTime.Today;
                var age = today.Year - DateOfBirth.Year;
                if (DateOfBirth.Date > today.AddYears(-age))
                    age--;
                return age;
            }
        }

        public DateTime CreatedAt { get; set; } = DateTime.Now;
        public DateTime UpdatedAt { get; set; } = DateTime.Now;
    }
}
