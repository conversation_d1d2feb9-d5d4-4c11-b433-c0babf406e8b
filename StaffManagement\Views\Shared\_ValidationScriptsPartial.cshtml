<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.19.5/jquery.validate.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validation-unobtrusive/3.2.12/jquery.validate.unobtrusive.min.js"></script>

<script>
    // تخصيص رسائل التحقق باللغة العربية
    jQuery.extend(jQuery.validator.messages, {
        required: "هذا الحقل مطلوب",
        remote: "يرجى تصحيح هذا الحقل",
        email: "يرجى إدخال عنوان بريد إلكتروني صحيح",
        url: "يرجى إدخال رابط صحيح",
        date: "يرجى إدخال تاريخ صحيح",
        dateISO: "يرجى إدخال تاريخ صحيح (ISO)",
        number: "يرجى إدخال رقم صحيح",
        digits: "يرجى إدخال أرقام فقط",
        creditcard: "يرجى إدخال رقم بطاقة ائتمان صحيح",
        equalTo: "يرجى إدخال نفس القيمة مرة أخرى",
        accept: "يرجى إدخال قيمة بامتداد صحيح",
        maxlength: jQuery.validator.format("يرجى عدم إدخال أكثر من {0} حرف"),
        minlength: jQuery.validator.format("يرجى إدخال {0} أحرف على الأقل"),
        rangelength: jQuery.validator.format("يرجى إدخال قيمة بين {0} و {1} حرف"),
        range: jQuery.validator.format("يرجى إدخال قيمة بين {0} و {1}"),
        max: jQuery.validator.format("يرجى إدخال قيمة أقل من أو تساوي {0}"),
        min: jQuery.validator.format("يرجى إدخال قيمة أكبر من أو تساوي {0}")
    });
</script>
