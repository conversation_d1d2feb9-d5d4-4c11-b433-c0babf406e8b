# دليل الاستخدام - نظام إدارة الضباط والمنتسبين

## 🚀 بدء التشغيل

### 1. تشغيل التطبيق
```bash
cd StaffManagement
dotnet run --urls "http://localhost:5000"
```

### 2. فتح المتصفح
انتقل إلى: `http://localhost:5000`

---

## 📋 الميزات الرئيسية

### ✅ إضافة ضابط جديد

1. **انقر على "إضافة ضابط جديد"** من الصفحة الرئيسية أو الشريط الجانبي
2. **رفع الصورة** (اختياري):
   - انقر على زر "اختيار صورة الموظف" في الأعلى
   - اختر صورة بصيغة JPG, PNG أو GIF
   - ستظهر معاينة فورية للصورة
3. **ملء البيانات الأساسية**:
   - رقم الموظف (مطلوب - يجب أن يكون فريد)
   - الرقم الإحصائي (مطلوب - يجب أن يكون فريد)
   - الرتبة (اختر من القائمة المنسدلة)
   - الاسم الرباعي واللقب
4. **ملء المعلومات الشخصية**:
   - محل الولادة
   - تاريخ الولادة (سيتم حساب العمر تلقائياً)
5. **انقر على "حفظ البيانات"**

### ✅ عرض قائمة الضباط

- **البحث**: استخدم مربع البحث للبحث بالاسم، رقم الموظف، الرقم الإحصائي، أو الرتبة
- **الترتيب**: انقر على عناوين الأعمدة لترتيب النتائج
- **العمليات**: لكل ضابط يمكنك:
  - 👁️ عرض التفاصيل
  - ✏️ تعديل البيانات
  - 🗑️ حذف الضابط

### ✅ عرض تفاصيل الضابط

- عرض شامل لجميع بيانات الضابط
- عرض الصورة الشخصية (إن وجدت)
- حساب العمر التفصيلي (سنوات، شهور، أيام)
- معلومات النظام (تاريخ الإضافة وآخر تحديث)
- إمكانية الطباعة

### ✅ تعديل بيانات الضابط

1. **انقر على زر "تعديل"** من قائمة الضباط أو صفحة التفاصيل
2. **تغيير الصورة** (اختياري):
   - ستظهر الصورة الحالية في الأعلى
   - انقر على "تغيير صورة الموظف" لاختيار صورة جديدة
   - ستظهر معاينة للصورة الجديدة
3. **تعديل البيانات** حسب الحاجة
4. **انقر على "حفظ التعديلات"**

### ✅ حذف ضابط

1. **انقر على زر "حذف"** من قائمة الضباط أو صفحة التفاصيل
2. **مراجعة البيانات** المراد حذفها
3. **كتابة "تأكيد الحذف"** في المربع المخصص
4. **انقر على "تأكيد الحذف"**
5. **تأكيد نهائي** في النافذة المنبثقة

---

## 🎨 واجهة المستخدم

### الشريط العلوي
- **الشعار**: العودة للصفحة الرئيسية
- **قائمة الضباط**: عرض جميع الضباط
- **إضافة ضابط**: إضافة ضابط جديد
- **الإعدادات**: خيارات إضافية (قيد التطوير)

### الشريط الجانبي
- **الصفحة الرئيسية**: العودة للقائمة الرئيسية
- **إدارة الضباط**: عرض وإدارة الضباط
- **إضافة ضابط جديد**: إضافة ضابط جديد
- **الإحصائيات**: عرض الإحصائيات (قيد التطوير)
- **التقارير**: إنشاء التقارير (قيد التطوير)

### التذييل
- معلومات النظام
- التاريخ والوقت الحالي

---

## 🔍 البحث والفلترة

### البحث السريع
- اكتب في مربع البحث أي من:
  - جزء من الاسم
  - رقم الموظف
  - الرقم الإحصائي
  - الرتبة
- انقر على "بحث" أو اضغط Enter

### الترتيب
- **الاسم**: ترتيب أبجدي أو عكسي
- **الرتبة**: ترتيب حسب الرتبة
- **العمر**: ترتيب حسب العمر (الأصغر أو الأكبر)

---

## 📊 حساب العمر

النظام يحسب العمر تلقائياً بطريقتين:

### العمر التفصيلي
- **مثال**: "35 سنة، 8 شهور، 15 يوم"
- يتم تحديثه تلقائياً كل يوم

### العمر بالسنوات
- **مثال**: "35 سنة"
- للاستخدام في الإحصائيات والتقارير

---

## 🖼️ إدارة الصور

### رفع الصور
- **الصيغ المدعومة**: JPG, JPEG, PNG, GIF
- **الحد الأقصى للحجم**: يُنصح بأقل من 5 ميجابايت
- **الأبعاد المُنصح بها**: 400x400 بكسل أو أكبر

### معاينة الصور
- معاينة فورية قبل الحفظ
- عرض دائري للصور
- صورة افتراضية عند عدم وجود صورة

### تخزين الصور
- يتم حفظ الصور في مجلد `wwwroot/images/officers/`
- أسماء فريدة لتجنب التضارب
- حذف تلقائي للصور القديمة عند التحديث

---

## ⚠️ تنبيهات مهمة

### الأمان
- **النسخ الاحتياطي**: قم بعمل نسخة احتياطية من قاعدة البيانات بانتظام
- **الصور**: تأكد من أن الصور لا تحتوي على معلومات حساسة

### الأداء
- **البحث**: استخدم كلمات مفتاحية محددة للحصول على نتائج أسرع
- **الصور**: استخدم صور بحجم معقول لتحسين الأداء

### التحقق من البيانات
- **رقم الموظف**: يجب أن يكون فريد
- **الرقم الإحصائي**: يجب أن يكون فريد
- **تاريخ الولادة**: لا يمكن أن يكون في المستقبل
- **العمر**: تحذير إذا كان أقل من 18 سنة

---

## 🛠️ استكشاف الأخطاء

### مشاكل شائعة

#### لا يمكن إضافة ضابط جديد
- **السبب**: رقم الموظف أو الرقم الإحصائي مكرر
- **الحل**: استخدم أرقام فريدة

#### لا تظهر الصورة
- **السبب**: صيغة الصورة غير مدعومة أو حجم كبير
- **الحل**: استخدم صيغة JPG أو PNG بحجم أقل من 5 ميجابايت

#### البحث لا يعمل
- **السبب**: أخطاء إملائية أو استخدام أحرف خاصة
- **الحل**: تأكد من الإملاء واستخدم كلمات بسيطة

#### التطبيق بطيء
- **السبب**: عدد كبير من الصور أو قاعدة بيانات كبيرة
- **الحل**: قم بتحسين الصور أو تنظيف قاعدة البيانات

---

## 📞 الدعم الفني

للحصول على المساعدة:
1. راجع هذا الدليل أولاً
2. تحقق من ملف README.md
3. ابحث في Issues على GitHub
4. أنشئ Issue جديد إذا لم تجد الحل

---

**نصائح للاستخدام الأمثل:**
- احتفظ بنسخة احتياطية من البيانات
- استخدم أرقام موظفين منطقية ومتسلسلة
- تأكد من دقة البيانات قبل الحفظ
- استخدم صور واضحة وحديثة للموظفين
