@model IEnumerable<StaffManagement.Models.Officer>

@{
    ViewData["Title"] = "قائمة الضباط والمنتسبين";
}

<div class="container-fluid" dir="rtl">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-users me-2"></i>
                        إدارة الضباط والمنتسبين
                    </h3>
                </div>
                <div class="card-body">
                    <!-- شريط البحث والإضافة -->
                    <div class="row mb-3">
                        <div class="col-md-8">
                            <form asp-action="Index" method="get" class="d-flex">
                                <input type="text" name="SearchString" value="@ViewData["CurrentFilter"]"
                                       class="form-control me-2" placeholder="البحث بالاسم، رقم الموظف، الرقم الإحصائي، أو الرتبة..." />
                                <button type="submit" class="btn btn-outline-primary">
                                    <i class="fas fa-search"></i> بحث
                                </button>
                                <a asp-action="Index" class="btn btn-outline-secondary ms-2">
                                    <i class="fas fa-times"></i> مسح
                                </a>
                            </form>
                        </div>
                        <div class="col-md-4 text-start">
                            <a asp-action="Create" class="btn btn-success">
                                <i class="fas fa-plus"></i> إضافة ضابط جديد
                            </a>
                        </div>
                    </div>

                    @if (TempData["SuccessMessage"] != null)
                    {
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="fas fa-check-circle me-2"></i>
                            @TempData["SuccessMessage"]
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    }

                    <!-- جدول البيانات -->
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>الصورة</th>
                                    <th>
                                        <a asp-action="Index" asp-route-sortOrder="@ViewData["NameSortParm"]"
                                           asp-route-searchString="@ViewData["CurrentFilter"]" class="text-white text-decoration-none">
                                            الاسم الرباعي واللقب
                                            <i class="fas fa-sort"></i>
                                        </a>
                                    </th>
                                    <th>
                                        <a asp-action="Index" asp-route-sortOrder="@ViewData["RankSortParm"]"
                                           asp-route-searchString="@ViewData["CurrentFilter"]" class="text-white text-decoration-none">
                                            الرتبة
                                            <i class="fas fa-sort"></i>
                                        </a>
                                    </th>
                                    <th>التسلسل</th>
                                    <th>الرقم الإحصائي</th>
                                    <th>محل الولادة</th>
                                    <th>تاريخ الولادة</th>
                                    <th>
                                        <a asp-action="Index" asp-route-sortOrder="@ViewData["AgeSortParm"]"
                                           asp-route-searchString="@ViewData["CurrentFilter"]" class="text-white text-decoration-none">
                                            العمر
                                            <i class="fas fa-sort"></i>
                                        </a>
                                    </th>
                                    <th>العمليات</th>
                                </tr>
                            </thead>
                            <tbody>
                                @if (Model.Any())
                                {
                                    @foreach (var item in Model)
                                    {
                                        <tr>
                                            <td>
                                                @if (!string.IsNullOrEmpty(item.PhotoPath))
                                                {
                                                    <img src="@item.PhotoPath" alt="صورة @item.FullName"
                                                         class="rounded-circle" style="width: 50px; height: 50px; object-fit: cover;" />
                                                }
                                                else
                                                {
                                                    <div class="bg-secondary rounded-circle d-flex align-items-center justify-content-center"
                                                         style="width: 50px; height: 50px;">
                                                        <i class="fas fa-user text-white"></i>
                                                    </div>
                                                }
                                            </td>
                                            <td class="fw-bold">@Html.DisplayFor(modelItem => item.FullName)</td>
                                            <td>
                                                <span class="badge bg-info">@Html.DisplayFor(modelItem => item.Rank)</span>
                                            </td>
                                            <td>@Html.DisplayFor(modelItem => item.EmployeeNumber)</td>
                                            <td>@Html.DisplayFor(modelItem => item.StatisticalNumber)</td>
                                            <td>@Html.DisplayFor(modelItem => item.PlaceOfBirth)</td>
                                            <td>@Html.DisplayFor(modelItem => item.DateOfBirth)</td>
                                            <td>
                                                <small class="text-muted">@Html.DisplayFor(modelItem => item.Age)</small>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a asp-action="Details" asp-route-id="@item.Id"
                                                       class="btn btn-sm btn-outline-info" title="عرض التفاصيل">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a asp-action="Edit" asp-route-id="@item.Id"
                                                       class="btn btn-sm btn-outline-warning" title="تعديل">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <a asp-action="Delete" asp-route-id="@item.Id"
                                                       class="btn btn-sm btn-outline-danger" title="حذف">
                                                        <i class="fas fa-trash"></i>
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    }
                                }
                                else
                                {
                                    <tr>
                                        <td colspan="9" class="text-center py-4">
                                            <div class="text-muted">
                                                <i class="fas fa-users fa-3x mb-3"></i>
                                                <h5>لا توجد بيانات</h5>
                                                <p>لم يتم العثور على أي ضباط أو منتسبين.</p>
                                                <a asp-action="Create" class="btn btn-primary">
                                                    <i class="fas fa-plus"></i> إضافة أول ضابط
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>

                    @if (Model.Any())
                    {
                        <div class="mt-3">
                            <small class="text-muted">
                                <i class="fas fa-info-circle"></i>
                                إجمالي عدد الضباط والمنتسبين: <strong>@Model.Count()</strong>
                            </small>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        // إخفاء رسائل النجاح تلقائياً بعد 5 ثوان
        setTimeout(function() {
            $('.alert-success').fadeOut('slow');
        }, 5000);
    </script>
}
