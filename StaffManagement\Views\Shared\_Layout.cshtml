<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - نظام إدارة الضباط والمنتسبين</title>

    <!-- Bootstrap CSS (RTL) -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Google Fonts - Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">

    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background-color: #f8f9fa;
        }

        .navbar-brand {
            font-weight: 700;
            font-size: 1.5rem;
        }

        .card {
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            border: 1px solid rgba(0, 0, 0, 0.125);
        }

        .table th {
            background-color: #495057;
            color: white;
            font-weight: 600;
        }

        .btn {
            font-weight: 500;
        }

        .footer {
            background-color: #343a40;
            color: white;
            padding: 2rem 0;
            margin-top: 3rem;
        }

        .sidebar {
            background-color: #2c3e50;
            min-height: calc(100vh - 56px);
            padding: 1rem 0;
        }

        .sidebar .nav-link {
            color: #ecf0f1;
            padding: 0.75rem 1.5rem;
            border-radius: 0;
            transition: all 0.3s ease;
        }

        .sidebar .nav-link:hover {
            background-color: #34495e;
            color: white;
        }

        .sidebar .nav-link.active {
            background-color: #3498db;
            color: white;
        }

        .main-content {
            padding: 2rem;
        }

        @@media (max-width: 768px) {
            .sidebar {
                min-height: auto;
            }

            .main-content {
                padding: 1rem;
            }
        }

        /* تحسينات للطباعة */
        @@media print {
            .sidebar, .navbar, .footer, .btn, .no-print {
                display: none !important;
            }

            .main-content {
                padding: 0;
                margin: 0;
            }

            .card {
                border: none;
                box-shadow: none;
            }
        }
    </style>
</head>
<body>
    <!-- شريط التنقل العلوي -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="/">
                <i class="fas fa-shield-alt me-2"></i>
                نظام إدارة الضباط والمنتسبين
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" asp-controller="Officers" asp-action="Index">
                            <i class="fas fa-users me-1"></i>
                            قائمة الضباط
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" asp-controller="Officers" asp-action="Create">
                            <i class="fas fa-user-plus me-1"></i>
                            إضافة ضابط
                        </a>
                    </li>
                </ul>

                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-cog me-1"></i>
                            الإعدادات
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#"><i class="fas fa-database me-2"></i>نسخ احتياطي</a></li>
                            <li><a class="dropdown-item" href="#"><i class="fas fa-file-export me-2"></i>تصدير البيانات</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#"><i class="fas fa-info-circle me-2"></i>حول النظام</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <!-- الشريط الجانبي -->
            <nav class="col-md-2 d-md-block sidebar collapse">
                <div class="position-sticky">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" asp-controller="Officers" asp-action="Index">
                                <i class="fas fa-home me-2"></i>
                                الصفحة الرئيسية
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" asp-controller="Officers" asp-action="Index">
                                <i class="fas fa-users me-2"></i>
                                إدارة الضباط
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" asp-controller="Officers" asp-action="Create">
                                <i class="fas fa-user-plus me-2"></i>
                                إضافة ضابط جديد
                            </a>
                        </li>
                        <li class="nav-item">
                            <hr class="text-light">
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="showStatistics()">
                                <i class="fas fa-chart-bar me-2"></i>
                                الإحصائيات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="showReports()">
                                <i class="fas fa-file-alt me-2"></i>
                                التقارير
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- المحتوى الرئيسي -->
            <main class="col-md-10 ms-sm-auto main-content">
                @RenderBody()
            </main>
        </div>
    </div>

    <!-- تذييل الصفحة -->
    <footer class="footer mt-auto no-print">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5>نظام إدارة الضباط والمنتسبين</h5>
                    <p class="mb-0">نظام شامل لإدارة بيانات الضباط والمنتسبين بكفاءة وأمان</p>
                </div>
                <div class="col-md-6 text-end">
                    <p class="mb-0">
                        <i class="fas fa-calendar me-2"></i>
                        تاريخ اليوم: <span id="currentDate"></span>
                    </p>
                    <p class="mb-0">
                        <i class="fas fa-clock me-2"></i>
                        الوقت: <span id="currentTime"></span>
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // تحديث التاريخ والوقت
        function updateDateTime() {
            const now = new Date();
            const dateOptions = {
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                weekday: 'long'
            };
            const timeOptions = {
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit',
                hour12: true
            };

            document.getElementById('currentDate').textContent =
                now.toLocaleDateString('ar-SA', dateOptions);
            document.getElementById('currentTime').textContent =
                now.toLocaleTimeString('ar-SA', timeOptions);
        }

        // تحديث كل ثانية
        setInterval(updateDateTime, 1000);
        updateDateTime();

        // تفعيل الرابط النشط في الشريط الجانبي
        document.addEventListener('DOMContentLoaded', function() {
            const currentPath = window.location.pathname;
            const navLinks = document.querySelectorAll('.sidebar .nav-link');

            navLinks.forEach(link => {
                if (link.getAttribute('href') === currentPath) {
                    link.classList.add('active');
                }
            });
        });

        // وظائف الإحصائيات والتقارير (يمكن تطويرها لاحقاً)
        function showStatistics() {
            alert('ميزة الإحصائيات قيد التطوير');
        }

        function showReports() {
            alert('ميزة التقارير قيد التطوير');
        }

        // تأكيد العمليات الحساسة
        function confirmAction(message) {
            return confirm(message || 'هل أنت متأكد من هذا الإجراء؟');
        }
    </script>

    @await RenderSectionAsync("Scripts", required: false)
</body>
</html>
