@model StaffManagement.Models.Officer

@{
    ViewData["Title"] = "تعديل بيانات الضابط";
}

<div class="container" dir="rtl">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header bg-warning text-dark">
                    <h4 class="card-title mb-0">
                        <i class="fas fa-user-edit me-2"></i>
                        تعديل بيانات الضابط
                    </h4>
                </div>
                <div class="card-body">
                    <form asp-action="Edit" method="post" enctype="multipart/form-data">
                        <div asp-validation-summary="ModelOnly" class="alert alert-danger"></div>
                        
                        <input type="hidden" asp-for="Id" />
                        <input type="hidden" asp-for="PhotoPath" />
                        <input type="hidden" asp-for="CreatedAt" />
                        
                        <div class="row">
                            <!-- الصورة الحالية -->
                            <div class="col-md-12 text-center mb-4">
                                <div class="current-photo">
                                    @if (!string.IsNullOrEmpty(Model.PhotoPath))
                                    {
                                        <img src="@Model.PhotoPath" alt="صورة @Model.FullName" 
                                             class="img-thumbnail rounded-circle" 
                                             style="width: 150px; height: 150px; object-fit: cover;" />
                                        <p class="text-muted mt-2">الصورة الحالية</p>
                                    }
                                    else
                                    {
                                        <div class="bg-secondary rounded-circle d-flex align-items-center justify-content-center mx-auto" 
                                             style="width: 150px; height: 150px;">
                                            <i class="fas fa-user fa-3x text-white"></i>
                                        </div>
                                        <p class="text-muted mt-2">لا توجد صورة</p>
                                    }
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <!-- معلومات أساسية -->
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="EmployeeNumber" class="form-label"></label>
                                    <input asp-for="EmployeeNumber" class="form-control" />
                                    <span asp-validation-for="EmployeeNumber" class="text-danger"></span>
                                </div>

                                <div class="mb-3">
                                    <label asp-for="StatisticalNumber" class="form-label"></label>
                                    <input asp-for="StatisticalNumber" class="form-control" />
                                    <span asp-validation-for="StatisticalNumber" class="text-danger"></span>
                                </div>

                                <div class="mb-3">
                                    <label asp-for="Rank" class="form-label"></label>
                                    <select asp-for="Rank" class="form-select">
                                        <option value="">اختر الرتبة</option>
                                        <option value="فريق أول">فريق أول</option>
                                        <option value="فريق">فريق</option>
                                        <option value="لواء">لواء</option>
                                        <option value="عميد">عميد</option>
                                        <option value="عقيد">عقيد</option>
                                        <option value="مقدم">مقدم</option>
                                        <option value="رائد">رائد</option>
                                        <option value="نقيب">نقيب</option>
                                        <option value="ملازم أول">ملازم أول</option>
                                        <option value="ملازم">ملازم</option>
                                        <option value="وكيل رقيب أول">وكيل رقيب أول</option>
                                        <option value="رقيب أول">رقيب أول</option>
                                        <option value="رقيب">رقيب</option>
                                        <option value="عريف">عريف</option>
                                        <option value="جندي أول">جندي أول</option>
                                        <option value="جندي">جندي</option>
                                    </select>
                                    <span asp-validation-for="Rank" class="text-danger"></span>
                                </div>

                                <div class="mb-3">
                                    <label asp-for="FullName" class="form-label"></label>
                                    <input asp-for="FullName" class="form-control" />
                                    <span asp-validation-for="FullName" class="text-danger"></span>
                                </div>
                            </div>

                            <!-- معلومات شخصية -->
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="PlaceOfBirth" class="form-label"></label>
                                    <input asp-for="PlaceOfBirth" class="form-control" />
                                    <span asp-validation-for="PlaceOfBirth" class="text-danger"></span>
                                </div>

                                <div class="mb-3">
                                    <label asp-for="DateOfBirth" class="form-label"></label>
                                    <input asp-for="DateOfBirth" class="form-control" type="date" />
                                    <span asp-validation-for="DateOfBirth" class="text-danger"></span>
                                </div>

                                <div class="mb-3">
                                    <label for="photo" class="form-label">تغيير صورة الموظف</label>
                                    <input type="file" name="photo" id="photo" class="form-control" accept="image/*" />
                                    <div class="form-text">اختر صورة جديدة لتغيير الصورة الحالية (اختياري)</div>
                                </div>

                                <!-- معاينة الصورة الجديدة -->
                                <div class="mb-3">
                                    <div id="imagePreview" class="text-center" style="display: none;">
                                        <img id="preview" src="#" alt="معاينة الصورة الجديدة" 
                                             class="img-thumbnail" style="max-width: 200px; max-height: 200px;" />
                                        <p class="text-success mt-2">الصورة الجديدة</p>
                                    </div>
                                </div>

                                <!-- عرض العمر الحالي -->
                                <div class="mb-3">
                                    <div class="alert alert-info">
                                        <strong>العمر الحالي:</strong> @Model.Age
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- أزرار العمليات -->
                        <div class="row">
                            <div class="col-12">
                                <hr />
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <button type="submit" class="btn btn-warning">
                                            <i class="fas fa-save me-2"></i>
                                            حفظ التعديلات
                                        </button>
                                        <a asp-action="Details" asp-route-id="@Model.Id" class="btn btn-info ms-2">
                                            <i class="fas fa-eye me-2"></i>
                                            عرض التفاصيل
                                        </a>
                                    </div>
                                    <div>
                                        <a asp-action="Index" class="btn btn-outline-primary">
                                            <i class="fas fa-arrow-left me-2"></i>
                                            العودة للقائمة
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    
    <script>
        // معاينة الصورة الجديدة قبل الرفع
        document.getElementById('photo').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    document.getElementById('preview').src = e.target.result;
                    document.getElementById('imagePreview').style.display = 'block';
                };
                reader.readAsDataURL(file);
            } else {
                document.getElementById('imagePreview').style.display = 'none';
            }
        });

        // التحقق من تاريخ الولادة
        document.getElementById('DateOfBirth').addEventListener('change', function(e) {
            const birthDate = new Date(e.target.value);
            const today = new Date();
            
            if (birthDate > today) {
                alert('تاريخ الولادة لا يمكن أن يكون في المستقبل');
                e.target.focus();
                return;
            }
            
            const age = today.getFullYear() - birthDate.getFullYear();
            const monthDiff = today.getMonth() - birthDate.getMonth();
            
            if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
                age--;
            }
            
            if (age < 18) {
                if (!confirm('العمر أقل من 18 سنة. هل أنت متأكد من التاريخ؟')) {
                    e.target.focus();
                }
            }
        });

        // تأكيد التعديل
        document.querySelector('form').addEventListener('submit', function(e) {
            if (!confirm('هل أنت متأكد من حفظ التعديلات؟')) {
                e.preventDefault();
            }
        });
    </script>
}
