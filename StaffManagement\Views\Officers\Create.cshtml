@model StaffManagement.Models.Officer

@{
    ViewData["Title"] = "إضافة ضابط جديد";
}

<div class="container" dir="rtl">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h4 class="card-title mb-0">
                        <i class="fas fa-user-plus me-2"></i>
                        إضافة ضابط جديد
                    </h4>
                </div>
                <div class="card-body">
                    <form asp-action="Create" method="post" enctype="multipart/form-data">
                        <div asp-validation-summary="ModelOnly" class="alert alert-danger"></div>

                        <!-- قسم الصورة في الأعلى -->
                        <div class="row mb-4">
                            <div class="col-12 text-center">
                                <div class="card border-2 border-dashed border-primary bg-light" style="min-height: 250px;">
                                    <div class="card-body d-flex flex-column justify-content-center align-items-center">
                                        <div id="imagePreview" style="display: none;">
                                            <img id="preview" src="#" alt="معاينة الصورة"
                                                 class="img-thumbnail rounded-circle mb-3"
                                                 style="width: 200px; height: 200px; object-fit: cover;" />
                                        </div>
                                        <div id="placeholderImage" class="text-center">
                                            <div class="bg-secondary rounded-circle d-flex align-items-center justify-content-center mx-auto mb-3"
                                                 style="width: 200px; height: 200px;">
                                                <i class="fas fa-camera fa-4x text-white"></i>
                                            </div>
                                        </div>
                                        <div class="mb-3">
                                            <label for="photo" class="btn btn-primary btn-lg">
                                                <i class="fas fa-upload me-2"></i>
                                                اختيار صورة الموظف
                                            </label>
                                            <input type="file" name="photo" id="photo" class="d-none" accept="image/*" />
                                        </div>
                                        <small class="text-muted">اختر صورة بصيغة JPG, PNG أو GIF (اختياري)</small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <!-- معلومات أساسية -->
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="EmployeeNumber" class="form-label"></label>
                                    <input asp-for="EmployeeNumber" class="form-control" placeholder="أدخل رقم الموظف" />
                                    <span asp-validation-for="EmployeeNumber" class="text-danger"></span>
                                </div>

                                <div class="mb-3">
                                    <label asp-for="StatisticalNumber" class="form-label"></label>
                                    <input asp-for="StatisticalNumber" class="form-control" placeholder="أدخل الرقم الإحصائي" />
                                    <span asp-validation-for="StatisticalNumber" class="text-danger"></span>
                                </div>

                                <div class="mb-3">
                                    <label asp-for="Rank" class="form-label"></label>
                                    <select asp-for="Rank" class="form-select">
                                        <option value="">اختر الرتبة</option>
                                        <option value="فريق أول">فريق أول</option>
                                        <option value="فريق">فريق</option>
                                        <option value="لواء">لواء</option>
                                        <option value="عميد">عميد</option>
                                        <option value="عقيد">عقيد</option>
                                        <option value="مقدم">مقدم</option>
                                        <option value="رائد">رائد</option>
                                        <option value="نقيب">نقيب</option>
                                        <option value="ملازم أول">ملازم أول</option>
                                        <option value="ملازم">ملازم</option>
                                        <option value="وكيل رقيب أول">وكيل رقيب أول</option>
                                        <option value="رقيب أول">رقيب أول</option>
                                        <option value="رقيب">رقيب</option>
                                        <option value="عريف">عريف</option>
                                        <option value="جندي أول">جندي أول</option>
                                        <option value="جندي">جندي</option>
                                    </select>
                                    <span asp-validation-for="Rank" class="text-danger"></span>
                                </div>

                                <div class="mb-3">
                                    <label asp-for="FullName" class="form-label"></label>
                                    <input asp-for="FullName" class="form-control" placeholder="أدخل الاسم الرباعي واللقب" />
                                    <span asp-validation-for="FullName" class="text-danger"></span>
                                </div>
                            </div>

                            <!-- معلومات شخصية -->
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="PlaceOfBirth" class="form-label"></label>
                                    <input asp-for="PlaceOfBirth" class="form-control" placeholder="أدخل محل الولادة" />
                                    <span asp-validation-for="PlaceOfBirth" class="text-danger"></span>
                                </div>

                                <div class="mb-3">
                                    <label asp-for="DateOfBirth" class="form-label"></label>
                                    <input asp-for="DateOfBirth" class="form-control" type="date" />
                                    <span asp-validation-for="DateOfBirth" class="text-danger"></span>
                                </div>


                            </div>
                        </div>

                        <!-- أزرار العمليات -->
                        <div class="row">
                            <div class="col-12">
                                <hr />
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <button type="submit" class="btn btn-success">
                                            <i class="fas fa-save me-2"></i>
                                            حفظ البيانات
                                        </button>
                                        <button type="reset" class="btn btn-secondary ms-2">
                                            <i class="fas fa-undo me-2"></i>
                                            إعادة تعيين
                                        </button>
                                    </div>
                                    <div>
                                        <a asp-action="Index" class="btn btn-outline-primary">
                                            <i class="fas fa-arrow-left me-2"></i>
                                            العودة للقائمة
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}

    <script>
        // معاينة الصورة قبل الرفع
        document.getElementById('photo').addEventListener('change', function(e) {
            const file = e.target.files[0];
            const imagePreview = document.getElementById('imagePreview');
            const placeholderImage = document.getElementById('placeholderImage');

            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    document.getElementById('preview').src = e.target.result;
                    imagePreview.style.display = 'block';
                    placeholderImage.style.display = 'none';
                };
                reader.readAsDataURL(file);
            } else {
                imagePreview.style.display = 'none';
                placeholderImage.style.display = 'block';
            }
        });

        // حساب العمر تلقائياً عند تغيير تاريخ الولادة
        document.getElementById('DateOfBirth').addEventListener('change', function(e) {
            const birthDate = new Date(e.target.value);
            const today = new Date();

            if (birthDate > today) {
                alert('تاريخ الولادة لا يمكن أن يكون في المستقبل');
                e.target.value = '';
                return;
            }

            const age = today.getFullYear() - birthDate.getFullYear();
            const monthDiff = today.getMonth() - birthDate.getMonth();

            if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
                age--;
            }

            if (age < 18) {
                if (confirm('العمر أقل من 18 سنة. هل أنت متأكد من التاريخ؟')) {
                    // المتابعة
                } else {
                    e.target.value = '';
                }
            }
        });
    </script>
}
